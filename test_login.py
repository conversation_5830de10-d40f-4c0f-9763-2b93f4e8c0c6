#!/usr/bin/env python3
"""
测试登录功能的脚本
用于验证 TeamRole 枚举映射问题是否已修复
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8080/api/v1"
TEST_EMAIL = "<EMAIL>"

def send_verification_code():
    """发送验证码"""
    url = f"{BASE_URL}/auth/send-verification-code"
    data = {
        "email": TEST_EMAIL,
        "type": "login"
    }
    
    print(f"发送验证码到: {TEST_EMAIL}")
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        print("✅ 验证码发送成功")
        return True
    else:
        print(f"❌ 验证码发送失败: {response.status_code} - {response.text}")
        return False

def test_login_with_code(code):
    """使用验证码登录"""
    url = f"{BASE_URL}/auth/login"
    data = {
        "email": TEST_EMAIL,
        "code": code
    }
    
    print(f"尝试登录: {TEST_EMAIL}")
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 登录成功!")
        print(f"用户信息: {result.get('user', {})}")
        print(f"团队数量: {len(result.get('teams', []))}")
        
        # 检查团队角色映射
        teams = result.get('teams', [])
        for i, team in enumerate(teams):
            print(f"团队 {i+1}: {team.get('name', 'Unknown')}")
            print(f"  - 角色: {team.get('role', 'Unknown')}")
            print(f"  - 是否创建者: {team.get('isCreator', 'Unknown')}")
        
        return result.get('token')
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def main():
    print("=== TeamRole 枚举映射测试 ===")
    
    # 发送验证码
    if not send_verification_code():
        return
    
    # 等待用户输入验证码
    print("\n请检查邮箱并输入收到的验证码:")
    code = input("验证码: ").strip()
    
    if not code:
        print("❌ 验证码不能为空")
        return
    
    # 尝试登录
    token = test_login_with_code(code)
    
    if token:
        print(f"\n✅ 测试完成! Token: {token[:50]}...")
        print("如果看到角色信息正确显示，说明 TeamRole 枚举映射问题已修复")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
