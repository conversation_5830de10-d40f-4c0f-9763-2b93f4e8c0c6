package com.teammanage.config;

import static org.junit.jupiter.api.Assertions.*;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.teammanage.enums.TeamRole;

/**
 * TeamRoleTypeHandler 简单测试类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class TeamRoleTypeHandlerSimpleTest {

    private TeamRoleTypeHandler typeHandler;
    private ResultSet resultSet;

    @BeforeEach
    void setUp() {
        typeHandler = new TeamRoleTypeHandler();
        resultSet = Mockito.mock(ResultSet.class);
    }

    @Test
    void testGetNullableResultWithValidValues() throws SQLException {
        // 测试权限级别 100 (TEAM_CREATOR)
        Mockito.when(resultSet.getInt("role")).thenReturn(100);
        Mockito.when(resultSet.wasNull()).thenReturn(false);
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        assertEquals(TeamRole.TEAM_CREATOR, result);

        // 测试权限级别 10 (TEAM_MEMBER)
        Mockito.when(resultSet.getInt("role")).thenReturn(10);
        Mockito.when(resultSet.wasNull()).thenReturn(false);
        result = typeHandler.getNullableResult(resultSet, "role");
        assertEquals(TeamRole.TEAM_MEMBER, result);
    }

    @Test
    void testGetNullableResultWithInvalidValues() throws SQLException {
        // 测试未知权限级别 999 (应该返回 TEAM_CREATOR，因为 >= 100)
        Mockito.when(resultSet.getInt("role")).thenReturn(999);
        Mockito.when(resultSet.wasNull()).thenReturn(false);
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        assertEquals(TeamRole.TEAM_CREATOR, result);

        // 测试未知权限级别 5 (应该返回 TEAM_MEMBER，因为 < 100)
        Mockito.when(resultSet.getInt("role")).thenReturn(5);
        Mockito.when(resultSet.wasNull()).thenReturn(false);
        result = typeHandler.getNullableResult(resultSet, "role");
        assertEquals(TeamRole.TEAM_MEMBER, result);
    }

    @Test
    void testGetNullableResultWithNullValue() throws SQLException {
        // 测试 null 值
        Mockito.when(resultSet.getInt("role")).thenReturn(0);
        Mockito.when(resultSet.wasNull()).thenReturn(true);
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        assertNull(result);
    }
}
