package com.teammanage.integration;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.mapper.TeamMemberMapper;

/**
 * TeamRole 枚举映射集成测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class TeamRoleIntegrationTest {

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Test
    void testTeamRoleMapping() {
        // 查询用户ID为8的团队成员信息（根据SQL数据，这个用户有角色值为100的记录）
        List<TeamMember> teamMembers = teamMemberMapper.findByAccountId(8L);
        
        assertNotNull(teamMembers, "团队成员列表不应为空");
        assertFalse(teamMembers.isEmpty(), "应该找到团队成员记录");
        
        // 检查角色映射是否正确
        for (TeamMember member : teamMembers) {
            assertNotNull(member.getRole(), "角色不应为空");
            
            // 如果数据库中的值是100，应该映射为TEAM_CREATOR
            if (member.getRole() == TeamRole.TEAM_CREATOR) {
                assertTrue(member.canManageTeam(), "团队创建者应该能管理团队");
                assertTrue(member.canManageMembers(), "团队创建者应该能管理成员");
            } else if (member.getRole() == TeamRole.TEAM_MEMBER) {
                assertFalse(member.canManageTeam(), "团队成员不应该能管理团队");
                assertFalse(member.canManageMembers(), "团队成员不应该能管理成员");
            }
            
            System.out.println("团队成员 ID: " + member.getId() + 
                             ", 角色: " + member.getRole() + 
                             ", 权限级别: " + member.getRole().getPermissionLevel());
        }
    }
    
    @Test
    void testSpecificTeamMemberRole() {
        // 测试特定的团队成员记录（ID=7，根据SQL数据应该是TEAM_CREATOR）
        TeamMember member = teamMemberMapper.selectById(7L);
        
        if (member != null) {
            assertNotNull(member.getRole(), "角色不应为空");
            assertEquals(TeamRole.TEAM_CREATOR, member.getRole(), "应该是团队创建者角色");
            assertEquals(100, member.getRole().getPermissionLevel(), "权限级别应该是100");
            
            System.out.println("特定团队成员测试 - ID: " + member.getId() + 
                             ", 角色: " + member.getRole() + 
                             ", 权限级别: " + member.getRole().getPermissionLevel());
        } else {
            System.out.println("未找到ID为7的团队成员记录");
        }
    }
}
