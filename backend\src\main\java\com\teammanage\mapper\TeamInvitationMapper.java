package com.teammanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.TeamInvitation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 团队邀请Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TeamInvitationMapper extends BaseMapper<TeamInvitation> {

    /**
     * 根据团队ID查询邀请列表
     * 
     * @param teamId 团队ID
     * @return 邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE team_id = #{teamId} ORDER BY created_at DESC")
    List<TeamInvitation> findByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据被邀请人邮箱查询邀请列表
     * 
     * @param email 邮箱
     * @return 邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE invitee_email = #{email} ORDER BY created_at DESC")
    List<TeamInvitation> findByInviteeEmail(@Param("email") String email);

    /**
     * 根据被邀请人ID查询邀请列表
     * 
     * @param inviteeId 被邀请人ID
     * @return 邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE invitee_id = #{inviteeId} ORDER BY created_at DESC")
    List<TeamInvitation> findByInviteeId(@Param("inviteeId") Long inviteeId);

    /**
     * 查询团队中指定邮箱的待处理邀请
     * 
     * @param teamId 团队ID
     * @param email 邮箱
     * @return 待处理邀请
     */
    @Select("SELECT * FROM team_invitation WHERE team_id = #{teamId} AND invitee_email = #{email} AND status = 1 AND expires_at > NOW()")
    TeamInvitation findPendingInvitation(@Param("teamId") Long teamId, @Param("email") String email);

    /**
     * 查询所有已过期但状态仍为PENDING的邀请
     * 
     * @return 过期邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE status = 1 AND expires_at <= NOW()")
    List<TeamInvitation> findExpiredInvitations();

    /**
     * 批量更新过期邀请状态
     * 
     * @param currentTime 当前时间
     * @return 更新数量
     */
    @Update("UPDATE team_invitation SET status = 4, updated_at = #{currentTime} WHERE status = 1 AND expires_at <= #{currentTime}")
    int updateExpiredInvitations(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据邀请人ID查询邀请列表
     * 
     * @param inviterId 邀请人ID
     * @return 邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE inviter_id = #{inviterId} ORDER BY created_at DESC")
    List<TeamInvitation> findByInviterId(@Param("inviterId") Long inviterId);

    /**
     * 查询团队的待处理邀请数量
     * 
     * @param teamId 团队ID
     * @return 待处理邀请数量
     */
    @Select("SELECT COUNT(*) FROM team_invitation WHERE team_id = #{teamId} AND status = 1 AND expires_at > NOW()")
    int countPendingInvitations(@Param("teamId") Long teamId);

    /**
     * 查询用户收到的待处理邀请数量
     * 
     * @param email 邮箱
     * @return 待处理邀请数量
     */
    @Select("SELECT COUNT(*) FROM team_invitation WHERE invitee_email = #{email} AND status = 1 AND expires_at > NOW()")
    int countPendingInvitationsByEmail(@Param("email") String email);

    /**
     * 根据团队ID和状态查询邀请列表
     * 
     * @param teamId 团队ID
     * @param status 状态
     * @return 邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE team_id = #{teamId} AND status = #{status} ORDER BY created_at DESC")
    List<TeamInvitation> findByTeamIdAndStatus(@Param("teamId") Long teamId, @Param("status") String status);

    /**
     * 根据邮箱和状态查询邀请列表
     * 
     * @param email 邮箱
     * @param status 状态
     * @return 邀请列表
     */
    @Select("SELECT * FROM team_invitation WHERE invitee_email = #{email} AND status = #{status} ORDER BY created_at DESC")
    List<TeamInvitation> findByEmailAndStatus(@Param("email") String email, @Param("status") String status);
}
